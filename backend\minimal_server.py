#!/usr/bin/env python3
"""
Minimal FastAPI server for testing without database connection.
"""

import sys
import os
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Set environment variable to skip database initialization
os.environ["SKIP_DB_INIT"] = "true"

def create_minimal_app():
    """Create a minimal FastAPI app for testing."""
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    
    app = FastAPI(
        title="Lonors AI Agent Platform (Minimal)",
        description="Minimal server for testing",
        version="0.1.0",
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": "Lonors AI Agent Platform - Minimal Server",
            "status": "running",
            "version": "0.1.0"
        }
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "service": "lonors-backend",
            "version": "0.1.0"
        }
    
    @app.get("/api/v1/status")
    async def api_status():
        """API status endpoint."""
        return {
            "api_version": "v1",
            "status": "operational",
            "features": {
                "database": "disabled",
                "auth": "disabled",
                "rag": "disabled"
            }
        }
    
    return app

def main():
    """Start the minimal server."""
    try:
        import uvicorn
        
        print("🚀 Starting Lonors AI Agent Platform - Minimal Server")
        print("📍 Server will be available at: http://localhost:3000")
        print("🔧 Environment: Development (Minimal)")
        print("⚠️  Database: Disabled for testing")
        print("-" * 60)
        
        app = create_minimal_app()
        
        # Start the server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=3000,
            reload=False,  # Disable reload for stability
            log_level="info",
            access_log=True,
        )
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("🔧 Make sure uvicorn is installed:")
        print("   uv add uvicorn")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Server Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
