#!/usr/bin/env python3
"""
Test the running server.
"""

import sys
import time
import json

def test_server():
    """Test the running server."""
    try:
        import httpx
        
        print("🔍 Testing server endpoints...")
        
        # Test root endpoint
        print("1. Testing root endpoint (/)...")
        response = httpx.get("http://localhost:3000/", timeout=5.0)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        # Test health endpoint
        print("\n2. Testing health endpoint (/health)...")
        response = httpx.get("http://localhost:3000/health", timeout=5.0)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        # Test API status endpoint
        print("\n3. Testing API status endpoint (/api/v1/status)...")
        response = httpx.get("http://localhost:3000/api/v1/status", timeout=5.0)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        print("\n✅ All server tests passed!")
        return True
        
    except ImportError:
        print("❌ httpx not available, trying with requests...")
        try:
            import requests
            
            print("🔍 Testing server endpoints with requests...")
            
            # Test root endpoint
            print("1. Testing root endpoint (/)...")
            response = requests.get("http://localhost:3000/", timeout=5.0)
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.json()}")
            
            print("\n✅ Server test passed!")
            return True
            
        except ImportError:
            print("❌ Neither httpx nor requests available")
            return False
        except Exception as e:
            print(f"❌ Server test failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Server test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Lonors AI Agent Platform Server")
    print("=" * 50)
    
    # Wait a moment for server to be ready
    time.sleep(1)
    
    success = test_server()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Server is responding correctly!")
    else:
        print("❌ Server test failed!")
    
    sys.exit(0 if success else 1)
