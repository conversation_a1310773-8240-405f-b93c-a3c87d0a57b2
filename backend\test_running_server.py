#!/usr/bin/env python3
"""
Test the running production server.
"""

import sys
import time
import json
import asyncio

async def test_server_async():
    """Test the running server using httpx."""
    try:
        import httpx
        
        print("🔍 Testing server endpoints with httpx...")
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Test root endpoint
            print("1. Testing root endpoint (/)...")
            response = await client.get("http://localhost:3000/")
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.json()}")
            
            # Test health endpoint
            print("\n2. Testing health endpoint (/health)...")
            response = await client.get("http://localhost:3000/health")
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.json()}")
            
            # Test API status endpoint
            print("\n3. Testing API status endpoint (/api/v1/status)...")
            response = await client.get("http://localhost:3000/api/v1/status")
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.json()}")
            
            # Test database health endpoint
            print("\n4. Testing database health endpoint (/api/v1/health/database)...")
            response = await client.get("http://localhost:3000/api/v1/health/database")
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.json()}")
        
        print("\n✅ All server tests passed!")
        return True
        
    except ImportError:
        print("❌ httpx not available")
        return False
    except Exception as e:
        print(f"❌ Server test failed: {e}")
        return False

def test_server_sync():
    """Test the running server using requests."""
    try:
        import requests
        
        print("🔍 Testing server endpoints with requests...")
        
        # Test root endpoint
        print("1. Testing root endpoint (/)...")
        response = requests.get("http://localhost:3000/", timeout=10.0)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        # Test health endpoint
        print("\n2. Testing health endpoint (/health)...")
        response = requests.get("http://localhost:3000/health", timeout=10.0)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
        
        print("\n✅ Basic server tests passed!")
        return True
        
    except ImportError:
        print("❌ requests not available")
        return False
    except Exception as e:
        print(f"❌ Server test failed: {e}")
        return False

async def main():
    """Main test function."""
    print("🚀 Testing Lonors AI Agent Platform Server")
    print("=" * 50)
    
    # Wait a moment for server to be ready
    time.sleep(2)
    
    # Try async first, then sync
    success = await test_server_async()
    
    if not success:
        print("\nTrying synchronous requests...")
        success = test_server_sync()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Server is responding correctly!")
        print("📖 API Documentation: http://localhost:3000/docs")
        print("🔍 Health Check: http://localhost:3000/health")
    else:
        print("❌ Server test failed!")
    
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        sys.exit(1)
