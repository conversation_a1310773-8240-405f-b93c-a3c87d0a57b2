Metadata-Version: 2.4
Name: lonors-backend
Version: 0.1.0
Summary: Lonors AI Agent Platform Backend
Project-URL: Homepage, https://github.com/lonors/lonors
Project-URL: Documentation, https://docs.lonors.ai
Project-URL: Repository, https://github.com/lonors/lonors
Project-URL: Issues, https://github.com/lonors/lonors/issues
Author-email: Lonors Team <<EMAIL>>
License: MIT
Keywords: agents,ai,fastapi,langchain,rag
Classifier: Development Status :: 3 - Alpha
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Requires-Python: >=3.11
Requires-Dist: aiofiles>=23.2.0
Requires-Dist: alembic>=1.13.0
Requires-Dist: anthropic>=0.7.0
Requires-Dist: anthropic>=0.8.0
Requires-Dist: asyncpg>=0.29.0
Requires-Dist: beautifulsoup4>=4.12.0
Requires-Dist: boto3>=1.34.0
Requires-Dist: celery>=5.3.0
Requires-Dist: cryptography>=41.0.0
Requires-Dist: easyocr>=1.7.0
Requires-Dist: email-validator>=2.2.0
Requires-Dist: fastapi>=0.104.0
Requires-Dist: flower>=2.0.0
Requires-Dist: httpx>=0.25.0
Requires-Dist: langchain-community>=0.0.10
Requires-Dist: langchain>=0.1.0
Requires-Dist: langgraph>=0.0.20
Requires-Dist: librosa>=0.10.1
Requires-Dist: lxml>=4.9.0
Requires-Dist: markdown>=3.5.0
Requires-Dist: minio>=7.2.0
Requires-Dist: neo4j>=5.15.0
Requires-Dist: numpy>=1.24.0
Requires-Dist: openai>=1.3.0
Requires-Dist: openai>=1.6.0
Requires-Dist: opencv-python>=4.8.0
Requires-Dist: passlib[bcrypt]>=1.7.4
Requires-Dist: pdf2image>=1.16.3
Requires-Dist: pillow>=10.1.0
Requires-Dist: prometheus-client>=0.19.0
Requires-Dist: pydantic-settings>=2.1.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: pydub>=0.25.1
Requires-Dist: pypdf>=3.17.0
Requires-Dist: pytesseract>=0.3.10
Requires-Dist: pytest-asyncio>=1.0.0
Requires-Dist: pytest-cov>=6.1.1
Requires-Dist: pytest>=8.3.5
Requires-Dist: python-docx>=1.1.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: python-jose[cryptography]>=3.3.0
Requires-Dist: python-magic>=0.4.27
Requires-Dist: python-multipart>=0.0.6
Requires-Dist: qdrant-client>=1.7.0
Requires-Dist: redis>=5.0.0
Requires-Dist: requests>=2.31.0
Requires-Dist: rich>=13.7.0
Requires-Dist: scrapy>=2.11.0
Requires-Dist: selenium>=4.16.0
Requires-Dist: sentence-transformers>=2.2.0
Requires-Dist: sentry-sdk[fastapi]>=1.38.0
Requires-Dist: soundfile>=0.12.1
Requires-Dist: sqlalchemy>=2.0.0
Requires-Dist: structlog>=23.2.0
Requires-Dist: tiktoken>=0.5.0
Requires-Dist: torch>=2.1.0
Requires-Dist: transformers>=4.36.0
Requires-Dist: typer>=0.9.0
Requires-Dist: uvicorn[standard]>=0.24.0
Requires-Dist: whisper>=1.1.10
Provides-Extra: dev
Requires-Dist: bandit>=1.7.0; extra == 'dev'
Requires-Dist: black>=23.11.0; extra == 'dev'
Requires-Dist: coverage>=7.3.0; extra == 'dev'
Requires-Dist: factory-boy>=3.3.0; extra == 'dev'
Requires-Dist: faker>=20.1.0; extra == 'dev'
Requires-Dist: httpx>=0.25.0; extra == 'dev'
Requires-Dist: ipython>=8.17.0; extra == 'dev'
Requires-Dist: isort>=5.12.0; extra == 'dev'
Requires-Dist: jupyter>=1.0.0; extra == 'dev'
Requires-Dist: mkdocs-material>=9.4.0; extra == 'dev'
Requires-Dist: mkdocs>=1.5.0; extra == 'dev'
Requires-Dist: mkdocstrings[python]>=0.24.0; extra == 'dev'
Requires-Dist: mypy>=1.7.0; extra == 'dev'
Requires-Dist: pre-commit>=3.6.0; extra == 'dev'
Requires-Dist: pytest-asyncio>=0.21.0; extra == 'dev'
Requires-Dist: pytest-benchmark>=4.0.0; extra == 'dev'
Requires-Dist: pytest-cov>=4.1.0; extra == 'dev'
Requires-Dist: pytest-mock>=3.12.0; extra == 'dev'
Requires-Dist: pytest-xdist>=3.5.0; extra == 'dev'
Requires-Dist: pytest>=7.4.0; extra == 'dev'
Requires-Dist: ruff>=0.1.6; extra == 'dev'
Requires-Dist: safety>=2.3.0; extra == 'dev'
Requires-Dist: semgrep>=1.45.0; extra == 'dev'
Description-Content-Type: text/markdown

# Lonors AI Agent Platform - Backend

## Overview

The backend for the Lonors AI Agent Platform is built with FastAPI and provides comprehensive APIs for document management, collaborative features, and AI-powered functionality.

## Features

- **Document Management**: Upload, process, and manage documents
- **Collaborative Features**: Document sharing, workspaces, comments, and activity tracking
- **AI Integration**: RAG (Retrieval-Augmented Generation), embeddings, and LLM processing
- **Authentication**: JWT-based authentication and authorization
- **Real-time Updates**: WebSocket support for live collaboration
- **Performance**: Optimized for high-throughput and low-latency operations

## Technology Stack

- **Framework**: FastAPI 0.104+
- **Language**: Python 3.11+
- **Package Manager**: uv
- **Database**: PostgreSQL 15+
- **Cache**: DragonflyDB/Redis
- **Vector Database**: Qdrant
- **Graph Database**: Neo4j
- **Testing**: pytest with 90%+ coverage

## Quick Start

### Prerequisites

- Python 3.11+
- uv package manager
- PostgreSQL
- Redis/DragonflyDB
- Qdrant

### Installation

```bash
# Install dependencies
uv sync

# Set up environment variables
cp .env.example .env

# Run database migrations
uv run python -c "import asyncio; from app.core.database import init_db; asyncio.run(init_db())"

# Start the server
uv run uvicorn app.main:app --host 0.0.0.0 --port 3000 --reload
```

### Docker Development

```bash
# Build and run with Docker
docker-compose up -d

# Check health
curl http://localhost:3000/health
```

## API Documentation

Once the server is running, visit:
- **Interactive API Docs**: http://localhost:3000/docs
- **ReDoc**: http://localhost:3000/redoc
- **OpenAPI Spec**: http://localhost:3000/openapi.json

## Testing

```bash
# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=app --cov-report=html

# Run specific test categories
uv run pytest -m unit
uv run pytest -m integration
uv run pytest -m collaborative
```

## Project Structure

```
backend/
├── app/
│   ├── api/           # API routes and endpoints
│   ├── core/          # Core configuration and database
│   ├── models/        # Database models
│   ├── services/      # Business logic services
│   ├── schemas/       # Pydantic schemas
│   └── main.py        # FastAPI application
├── tests/             # Test suites
├── scripts/           # Utility scripts
└── pyproject.toml     # Project configuration
```

## Environment Variables

Key environment variables:

```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/lonors
REDIS_URL=redis://localhost:6379
QDRANT_URL=http://localhost:6333

# Authentication
SECRET_KEY=your-secret-key
ALGORITHM=HS256

# Features
ENABLE_COLLABORATION=true
ENABLE_RAG=true

# AI/ML
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
```

## Collaborative Features

The backend includes comprehensive collaborative features:

- **Document Sharing**: Private, link, and public sharing with permissions
- **Workspaces**: Team collaboration with role-based access
- **Comments**: Rich commenting system with threading and annotations
- **Activity Tracking**: Comprehensive audit logs and real-time notifications

### API Endpoints

- `/api/v1/sharing/*` - Document sharing management
- `/api/v1/workspaces/*` - Workspace collaboration
- `/api/v1/comments/*` - Comments and annotations
- `/api/v1/activity/*` - Activity tracking and feeds

## Performance

- **API Response Time**: <200ms for most endpoints
- **Concurrent Users**: Supports 10,000+ simultaneous users
- **Database**: Optimized queries with connection pooling
- **Caching**: Redis-based caching for frequently accessed data

## Security

- **Authentication**: JWT tokens with refresh mechanism
- **Authorization**: Role-based access control (RBAC)
- **Input Validation**: Comprehensive request validation
- **SQL Injection**: Parameterized queries and ORM protection
- **CORS**: Configurable cross-origin resource sharing

## Monitoring

- **Health Checks**: `/health` and `/api/v1/health/collaboration`
- **Metrics**: Prometheus-compatible metrics
- **Logging**: Structured logging with correlation IDs
- **Tracing**: Distributed tracing support

## Development

### Code Quality

- **Linting**: Ruff for code formatting and linting
- **Type Checking**: MyPy for static type analysis
- **Testing**: pytest with 90%+ coverage requirement
- **Security**: Bandit for security vulnerability scanning

### Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass and coverage is maintained
5. Submit a pull request

## License

This project is licensed under the MIT License.
