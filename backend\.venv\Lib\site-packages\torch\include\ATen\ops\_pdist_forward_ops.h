#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API _pdist_forward {
  using schema = at::Tensor (const at::Tensor &, double);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_pdist_forward";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "_pdist_forward(Tensor self, float p=2) -> Tensor";
  static at::Tensor call(const at::Tensor & self, double p);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, double p);
};

struct TORCH_API _pdist_forward_out {
  using schema = at::Tensor & (const at::Tensor &, double, at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::_pdist_forward";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "_pdist_forward.out(Tensor self, float p=2, *, Tensor(a!) out) -> Tensor(a!)";
  static at::Tensor & call(const at::Tensor & self, double p, at::Tensor & out);
  static at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, double p, at::Tensor & out);
};

}} // namespace at::_ops
