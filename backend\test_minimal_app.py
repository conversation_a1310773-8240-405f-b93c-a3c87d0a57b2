#!/usr/bin/env python3
"""
Test minimal FastAPI app without database connection.
"""

import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_minimal_fastapi():
    """Test minimal FastAPI app creation."""
    try:
        print("1. Testing FastAPI import...")
        from fastapi import FastAPI
        print("✅ FastAPI imported successfully")
        
        print("2. Creating minimal app...")
        app = FastAPI(title="Test App")
        print("✅ Minimal FastAPI app created")
        
        print("3. Testing app configuration...")
        @app.get("/")
        def read_root():
            return {"message": "Hello World"}
        
        print("✅ Route added successfully")
        
        print("4. Testing uvicorn import...")
        import uvicorn
        print(f"✅ Uvicorn imported: {uvicorn.__version__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_config():
    """Test database configuration separately."""
    try:
        print("5. Testing database config import...")
        from app.core.config import settings
        print(f"✅ Config imported: {settings.DATABASE_URL}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database config error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_engine():
    """Test database engine creation separately."""
    try:
        print("6. Testing database engine...")
        from app.core.database import engine
        print("✅ Database engine created")
        
        return True
        
    except Exception as e:
        print(f"❌ Database engine error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 Testing Minimal FastAPI Components")
    print("=" * 50)
    
    success = True
    
    # Test minimal FastAPI
    if not test_minimal_fastapi():
        success = False
    
    print()
    
    # Test database config
    if not test_database_config():
        success = False
    
    print()
    
    # Test database engine (this might fail)
    if not test_database_engine():
        success = False
    
    print()
    print("=" * 50)
    if success:
        print("🎉 All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    sys.exit(0 if success else 1)
