"""User-related Pydantic schemas."""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, EmailStr, Field, field_validator

from app.models.user import UserRoleEnum


class UserBase(BaseModel):
    """Base user schema with common fields."""
    email: EmailStr = Field(..., description="User email address")
    username: str = Field(
        ..., 
        min_length=3, 
        max_length=50,
        description="Username"
    )
    full_name: Optional[str] = Field(None, max_length=255, description="Full name")
    bio: Optional[str] = Field(None, max_length=1000, description="User biography")
    avatar_url: Optional[str] = Field(None, description="Avatar image URL")


class UserCreate(UserBase):
    """Schema for creating a new user."""
    password: str = Field(..., min_length=8, description="Password")
    role: UserRoleEnum = Field(default=UserRoleEnum.USER, description="User role")
    
    @field_validator("password")
    @classmethod
    def validate_password_strength(cls, v):
        """Validate password strength."""
        from app.core.security import SecurityValidator

        is_valid, issues = SecurityValidator.validate_password_strength(v)
        if not is_valid:
            raise ValueError(f"Password validation failed: {'; '.join(issues)}")
        return v

    model_config = {
        "json_schema_extra": {
            "example": {
                "email": "<EMAIL>",
                "username": "johndoe",
                "password": "SecurePass123!",
                "full_name": "John Doe",
                "bio": "Software developer passionate about AI",
                "role": "user"
            }
        }
    }


class UserUpdate(BaseModel):
    """Schema for updating user information."""
    email: Optional[EmailStr] = Field(None, description="User email address")
    username: Optional[str] = Field(
        None, 
        min_length=3, 
        max_length=50,
        description="Username"
    )
    full_name: Optional[str] = Field(None, max_length=255, description="Full name")
    bio: Optional[str] = Field(None, max_length=1000, description="User biography")
    avatar_url: Optional[str] = Field(None, description="Avatar image URL")
    is_active: Optional[bool] = Field(None, description="User active status")
    
    class Config:
        schema_extra = {
            "example": {
                "full_name": "John Doe Updated",
                "bio": "Updated biography",
                "avatar_url": "https://example.com/avatar.jpg"
            }
        }


class UserInDB(UserBase):
    """Schema for user data stored in database."""
    id: int
    role: UserRoleEnum
    is_active: bool
    is_verified: bool
    is_superuser: bool
    created_at: datetime
    updated_at: datetime
    last_login_at: Optional[datetime]
    
    model_config = {"from_attributes": True}


class User(UserInDB):
    """Public user schema (excludes sensitive information)."""
    permissions: list[str] = Field(default_factory=list, description="User permissions")
    
    @field_validator("permissions", mode="before")
    @classmethod
    def set_permissions(cls, v, info):
        """Set permissions based on role."""
        # This would be calculated from the user's role
        # For now, return empty list
        return []

    model_config = {
        "json_schema_extra": {
            "example": {
                "id": 1,
                "email": "<EMAIL>",
                "username": "johndoe",
                "full_name": "John Doe",
                "bio": "Software developer",
                "avatar_url": "https://example.com/avatar.jpg",
                "role": "user",
                "is_active": True,
                "is_verified": True,
                "is_superuser": False,
                "permissions": ["read:own", "create:document"],
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "last_login_at": "2024-01-01T12:00:00Z"
            }
        }
    }


class UserPublic(BaseModel):
    """Public user information (minimal data for public display)."""
    id: int
    username: str
    full_name: Optional[str]
    avatar_url: Optional[str]
    bio: Optional[str]
    created_at: datetime
    
    model_config = {
        "from_attributes": True,
        "json_schema_extra": {
            "example": {
                "id": 1,
                "username": "johndoe",
                "full_name": "John Doe",
                "avatar_url": "https://example.com/avatar.jpg",
                "bio": "Software developer",
                "created_at": "2024-01-01T00:00:00Z"
            }
        }
    }


class UserStats(BaseModel):
    """User statistics schema."""
    document_count: int = Field(default=0, description="Number of documents")
    agent_count: int = Field(default=0, description="Number of agents")
    workflow_count: int = Field(default=0, description="Number of workflows")
    total_storage_mb: float = Field(default=0.0, description="Total storage used in MB")
    
    class Config:
        schema_extra = {
            "example": {
                "document_count": 25,
                "agent_count": 5,
                "workflow_count": 10,
                "total_storage_mb": 150.5
            }
        }
