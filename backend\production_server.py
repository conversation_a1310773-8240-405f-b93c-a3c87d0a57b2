#!/usr/bin/env python3
"""
Production-ready FastAPI server with conditional database initialization.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def create_app_without_db():
    """Create FastAPI app without database initialization."""
    from fastapi import FastAPI
    from fastapi.middleware.cors import CORSMiddleware
    from app.core.config import get_settings
    
    settings = get_settings()
    
    app = FastAPI(
        title="Lonors AI Agent Platform",
        description="AI-powered document processing and knowledge management platform",
        version="0.1.0",
        docs_url="/docs",
        redoc_url="/redoc",
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins_list,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": "Lonors AI Agent Platform",
            "status": "running",
            "version": "0.1.0",
            "docs": "/docs"
        }
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "service": "lonors-backend",
            "version": "0.1.0",
            "database": "not_initialized"
        }
    
    @app.get("/api/v1/status")
    async def api_status():
        """API status endpoint."""
        return {
            "api_version": "v1",
            "status": "operational",
            "features": {
                "database": "conditional",
                "auth": "available",
                "rag": "available"
            }
        }
    
    # Add database health check endpoint
    @app.get("/api/v1/health/database")
    async def database_health():
        """Database health check endpoint."""
        try:
            from app.core.database import check_db_connection
            is_healthy = await check_db_connection()
            return {
                "status": "healthy" if is_healthy else "unhealthy",
                "database": "connected" if is_healthy else "disconnected"
            }
        except Exception as e:
            return {
                "status": "error",
                "database": "error",
                "error": str(e)
            }
    
    return app

async def initialize_database_if_needed():
    """Initialize database if needed."""
    try:
        from app.core.database import init_db, setup_sqlite_pragma
        
        # Setup SQLite pragma if using SQLite
        setup_sqlite_pragma()
        
        # Initialize database
        await init_db()
        print("✅ Database initialized successfully")
        return True
    except Exception as e:
        print(f"⚠️  Database initialization failed: {e}")
        print("🔧 Server will run without database features")
        return False

def main():
    """Start the production server."""
    try:
        import uvicorn
        
        print("🚀 Starting Lonors AI Agent Platform - Production Server")
        print("📍 Server will be available at: http://localhost:3000")
        print("📖 API Documentation: http://localhost:3000/docs")
        print("🔧 Environment: Production")
        print("-" * 60)
        
        # Create app without database
        app = create_app_without_db()
        
        # Add startup event for database initialization
        @app.on_event("startup")
        async def startup_event():
            """Startup event handler."""
            print("🔄 Initializing application...")
            db_success = await initialize_database_if_needed()
            if db_success:
                print("✅ Application startup complete with database")
            else:
                print("⚠️  Application startup complete without database")
        
        @app.on_event("shutdown")
        async def shutdown_event():
            """Shutdown event handler."""
            try:
                from app.core.database import close_db
                await close_db()
                print("✅ Database connections closed")
            except Exception as e:
                print(f"⚠️  Error closing database: {e}")
        
        # Start the server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=3000,
            reload=False,  # Disable reload for production
            log_level="info",
            access_log=True,
        )
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("🔧 Make sure all dependencies are installed:")
        print("   uv sync --dev")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Server Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
