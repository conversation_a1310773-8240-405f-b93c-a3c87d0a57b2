import subprocess

def run_git_command(command):
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        return result.returncode, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return 1, "", str(e)

print("📋 Checking Git Status and Adding Files")
print("=" * 50)

# Check git status first
print("\n1. Checking current git status...")
returncode, stdout, stderr = run_git_command("git status --porcelain")

if returncode == 0:
    if stdout:
        lines = stdout.split('\n')
        print(f"✅ Found {len(lines)} files with changes:")
        for line in lines[:10]:  # Show first 10 files
            print(f"   {line}")
        if len(lines) > 10:
            print(f"   ... and {len(lines) - 10} more files")
    else:
        print("✅ Working directory is clean")
else:
    print(f"❌ Failed to get git status: {stderr}")
    exit(1)

# Add all files
print("\n2. Adding all files to staging area...")
returncode, stdout, stderr = run_git_command("git add .")

if returncode == 0:
    print("✅ Successfully added all files to staging area")
    if stdout:
        print(f"   Output: {stdout}")
else:
    print(f"❌ Failed to add files: {stderr}")
    exit(1)

# Check status after adding
print("\n3. Checking git status after adding files...")
returncode, stdout, stderr = run_git_command("git status --porcelain")

if returncode == 0:
    if stdout:
        lines = stdout.split('\n')
        staged_files = [line for line in lines if line.startswith('A ') or line.startswith('M ')]
        print(f"✅ {len(staged_files)} files staged for commit:")
        for line in staged_files[:10]:  # Show first 10 staged files
            print(f"   {line}")
        if len(staged_files) > 10:
            print(f"   ... and {len(staged_files) - 10} more staged files")
    else:
        print("✅ All files are staged (working directory clean)")
else:
    print(f"❌ Failed to get git status after adding: {stderr}")
    exit(1)

print("\n🎉 Git add operation completed successfully!")
print("Ready for commit.")
