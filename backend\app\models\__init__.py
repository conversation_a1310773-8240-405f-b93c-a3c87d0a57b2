"""Database models for the Lonors AI Agent Platform."""

from app.models.user import User, UserRole, UserSession
from app.models.document import Document, DocumentChunk, DocumentMetadata
from app.models.agent import Agent, AgentExecution, AgentConfiguration
from app.models.workflow import Workflow, WorkflowExecution, WorkflowStep
from app.models.knowledge_graph import Entity, Relationship, GraphNode
from app.models.password_entry import PasswordEntry, PasswordCategory

# Collaborative Features Models
from app.models.sharing import (
    DocumentShare, SharePermission, ShareType, ShareAccess, Workspace,
    WorkspaceMember, WorkspaceRole, WorkspaceDocument
)
from app.models.comments import (
    DocumentComment, CommentType, CommentStatus, CommentLike, DocumentAnnotation
)
from app.models.activity import (
    ActivityLog, ActivityType, ActivityLevel, ActivityFeed,
    NotificationPreference, ActivitySummary
)

__all__ = [
    # User models
    "User",
    "UserRole",
    "UserSession",

    # Document models
    "Document",
    "DocumentChunk",
    "DocumentMetadata",

    # Agent models
    "Agent",
    "AgentExecution",
    "AgentConfiguration",

    # Workflow models
    "Workflow",
    "WorkflowExecution",
    "WorkflowStep",

    # Knowledge graph models
    "Entity",
    "Relationship",
    "GraphNode",

    # Password manager models
    "PasswordEntry",
    "PasswordCategory",

    # Collaborative Features Models
    # Sharing models
    "DocumentShare",
    "SharePermission",
    "ShareType",
    "ShareAccess",
    "Workspace",
    "WorkspaceMember",
    "WorkspaceRole",
    "WorkspaceDocument",

    # Comments models
    "DocumentComment",
    "CommentType",
    "CommentStatus",
    "CommentLike",
    "DocumentAnnotation",

    # Activity models
    "ActivityLog",
    "ActivityType",
    "ActivityLevel",
    "ActivityFeed",
    "NotificationPreference",
    "ActivitySummary",
]
