"""Document-related Pydantic schemas."""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field, field_validator

from app.models.document import DocumentStatusEnum, DocumentTypeEnum


class DocumentUploadResponse(BaseModel):
    """Response schema for document upload."""
    id: int
    title: str
    filename: str
    document_type: DocumentTypeEnum
    status: DocumentStatusEnum
    file_size: int
    file_size_mb: float
    content_hash: str
    created_at: datetime
    
    model_config = {
        "from_attributes": True,
        "json_schema_extra": {
            "example": {
                "id": 1,
                "title": "Sample Document",
                "filename": "sample.pdf",
                "document_type": "pdf",
                "status": "uploaded",
                "file_size": 1048576,
                "file_size_mb": 1.0,
                "content_hash": "abc123...",
                "created_at": "2024-01-01T00:00:00Z"
            }
        }
    }


class DocumentBase(BaseModel):
    """Base document schema."""
    title: str = Field(..., min_length=1, max_length=500, description="Document title")
    tags: Optional[List[str]] = Field(None, description="Document tags")
    custom_metadata: Optional[dict] = Field(None, description="Custom metadata")


class DocumentCreate(DocumentBase):
    """Schema for creating a document."""
    pass


class DocumentUpdate(BaseModel):
    """Schema for updating a document."""
    title: Optional[str] = Field(None, min_length=1, max_length=500)
    tags: Optional[List[str]] = None
    custom_metadata: Optional[dict] = None


class Document(DocumentBase):
    """Complete document schema."""
    id: int
    filename: str
    file_path: str
    document_type: DocumentTypeEnum
    mime_type: str
    status: DocumentStatusEnum
    file_size: int
    file_size_mb: float
    content_hash: str
    language: Optional[str]
    embedding_model: Optional[str]
    embedding_dimensions: Optional[int]
    chunk_count: int
    processing_error: Optional[str]
    created_at: datetime
    updated_at: datetime
    processed_at: Optional[datetime]
    owner_id: int
    
    model_config = {"from_attributes": True}


class DocumentPublic(BaseModel):
    """Public document schema (limited information)."""
    id: int
    title: str
    filename: str
    document_type: DocumentTypeEnum
    status: DocumentStatusEnum
    file_size_mb: float
    chunk_count: int
    created_at: datetime
    processed_at: Optional[datetime]
    
    model_config = {"from_attributes": True}


class DocumentChunkBase(BaseModel):
    """Base document chunk schema."""
    content: str = Field(..., min_length=1, description="Chunk content")
    chunk_index: int = Field(..., ge=0, description="Chunk index in document")
    start_position: Optional[int] = Field(None, ge=0, description="Start position in document")
    end_position: Optional[int] = Field(None, ge=0, description="End position in document")
    page_number: Optional[int] = Field(None, ge=1, description="Page number")


class DocumentChunk(DocumentChunkBase):
    """Complete document chunk schema."""
    id: int
    document_id: int
    content_hash: str
    vector_id: Optional[str]
    embedding_model: Optional[str]
    token_count: Optional[int]
    character_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True


class DocumentProcessingStatus(BaseModel):
    """Document processing status schema."""
    document_id: int
    status: DocumentStatusEnum
    progress_percentage: Optional[float] = Field(None, ge=0, le=100)
    current_step: Optional[str] = None
    error_message: Optional[str] = None
    chunks_processed: int = Field(default=0, ge=0)
    total_chunks: Optional[int] = Field(None, ge=0)
    
    class Config:
        schema_extra = {
            "example": {
                "document_id": 1,
                "status": "processing",
                "progress_percentage": 75.0,
                "current_step": "generating_embeddings",
                "error_message": None,
                "chunks_processed": 15,
                "total_chunks": 20
            }
        }


class DocumentStats(BaseModel):
    """Document statistics schema."""
    total_documents: int = Field(default=0, ge=0)
    documents_by_type: dict = Field(default_factory=dict)
    documents_by_status: dict = Field(default_factory=dict)
    total_size_mb: float = Field(default=0.0, ge=0)
    total_chunks: int = Field(default=0, ge=0)
    average_chunks_per_document: float = Field(default=0.0, ge=0)
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "total_documents": 25,
                "documents_by_type": {
                    "pdf": 15,
                    "docx": 8,
                    "txt": 2
                },
                "documents_by_status": {
                    "processed": 20,
                    "processing": 3,
                    "failed": 2
                },
                "total_size_mb": 150.5,
                "total_chunks": 500,
                "average_chunks_per_document": 20.0
            }
        }
    }


class DocumentSearchQuery(BaseModel):
    """Document search query schema."""
    query: str = Field(..., min_length=1, max_length=1000, description="Search query")
    limit: int = Field(default=10, ge=1, le=100, description="Maximum results to return")
    threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Similarity threshold")
    document_ids: Optional[List[int]] = Field(None, description="Limit search to specific documents")
    document_types: Optional[List[DocumentTypeEnum]] = Field(None, description="Limit search to specific types")
    
    class Config:
        schema_extra = {
            "example": {
                "query": "machine learning algorithms",
                "limit": 10,
                "threshold": 0.7,
                "document_ids": [1, 2, 3],
                "document_types": ["pdf", "docx"]
            }
        }


class DocumentSearchResult(BaseModel):
    """Document search result schema."""
    chunk_id: int
    document_id: int
    document_title: str
    content: str
    similarity_score: float = Field(..., ge=0.0, le=1.0)
    chunk_index: int
    page_number: Optional[int]
    start_position: Optional[int]
    end_position: Optional[int]
    
    class Config:
        schema_extra = {
            "example": {
                "chunk_id": 123,
                "document_id": 1,
                "document_title": "Machine Learning Guide",
                "content": "Machine learning algorithms are computational methods...",
                "similarity_score": 0.85,
                "chunk_index": 5,
                "page_number": 3,
                "start_position": 1250,
                "end_position": 2250
            }
        }
