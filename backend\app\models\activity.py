"""Database models for activity tracking and audit logs."""

import enum
from datetime import datetime, timezone
from typing import Optional

from sqlalchemy import (
    <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateT<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, 
    String, Text, JSON, Index
)
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.core.database import Base


class ActivityType(str, enum.Enum):
    """Activity types for comprehensive tracking."""
    # Document activities
    DOCUMENT_CREATED = "document_created"
    DOCUMENT_UPDATED = "document_updated"
    DOCUMENT_DELETED = "document_deleted"
    DOCUMENT_VIEWED = "document_viewed"
    DOCUMENT_DOWNLOADED = "document_downloaded"
    DOCUMENT_PROCESSED = "document_processed"
    
    # Sharing activities
    DOCUMENT_SHARED = "document_shared"
    DOCUMENT_UNSHARED = "document_unshared"
    SHARE_ACCESSED = "share_accessed"
    SHARE_EXPIRED = "share_expired"
    
    # Comment activities
    COMMENT_CREATED = "comment_created"
    COMMENT_UPDATED = "comment_updated"
    COMMENT_DELETED = "comment_deleted"
    COMMENT_LIKED = "comment_liked"
    COMMENT_RESOLVED = "comment_resolved"
    
    # Workspace activities
    WORKSPACE_CREATED = "workspace_created"
    WORKSPACE_UPDATED = "workspace_updated"
    WORKSPACE_DELETED = "workspace_deleted"
    WORKSPACE_JOINED = "workspace_joined"
    WORKSPACE_LEFT = "workspace_left"
    WORKSPACE_MEMBER_ADDED = "workspace_member_added"
    WORKSPACE_MEMBER_REMOVED = "workspace_member_removed"
    WORKSPACE_ROLE_CHANGED = "workspace_role_changed"
    
    # AI activities
    AI_SUMMARY_GENERATED = "ai_summary_generated"
    AI_CATEGORIZATION = "ai_categorization"
    AI_CHAT_QUERY = "ai_chat_query"
    AI_ANALYSIS_COMPLETED = "ai_analysis_completed"
    
    # User activities
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    USER_PROFILE_UPDATED = "user_profile_updated"
    USER_PASSWORD_CHANGED = "user_password_changed"
    
    # System activities
    SYSTEM_BACKUP = "system_backup"
    SYSTEM_MAINTENANCE = "system_maintenance"
    SYSTEM_ERROR = "system_error"


class ActivityLevel(str, enum.Enum):
    """Activity importance levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ActivityLog(Base):
    """Comprehensive activity and audit log."""
    
    __tablename__ = "activity_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Activity identification
    activity_type = Column(Enum(ActivityType), nullable=False)
    activity_level = Column(Enum(ActivityLevel), default=ActivityLevel.MEDIUM, nullable=False)
    
    # Actor information
    user_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    session_id = Column(String(255), nullable=True)
    
    # Target resources
    document_id = Column(Integer, ForeignKey("documents.id", ondelete="SET NULL"), nullable=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id", ondelete="SET NULL"), nullable=True)
    comment_id = Column(Integer, ForeignKey("document_comments.id", ondelete="SET NULL"), nullable=True)
    
    # Activity details
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    activity_metadata = Column(JSON, nullable=True)  # Additional structured data
    
    # Request context
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    request_id = Column(String(100), nullable=True)
    
    # Geographic information (optional)
    country = Column(String(2), nullable=True)
    city = Column(String(100), nullable=True)
    
    # Performance metrics
    duration_ms = Column(Integer, nullable=True)  # Operation duration
    
    # Status and outcome
    success = Column(Boolean, default=True, nullable=False)
    error_message = Column(Text, nullable=True)
    error_code = Column(String(50), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="activities")
    document = relationship("Document")
    workspace = relationship("Workspace")
    comment = relationship("DocumentComment")
    
    # Indexes for efficient querying
    __table_args__ = (
        Index("idx_activity_logs_user", "user_id"),
        Index("idx_activity_logs_type", "activity_type"),
        Index("idx_activity_logs_level", "activity_level"),
        Index("idx_activity_logs_document", "document_id"),
        Index("idx_activity_logs_workspace", "workspace_id"),
        Index("idx_activity_logs_created", "created_at"),
        Index("idx_activity_logs_success", "success"),
        Index("idx_activity_logs_session", "session_id"),
        Index("idx_activity_logs_ip", "ip_address"),
        # Composite indexes for common queries
        Index("idx_activity_logs_user_type", "user_id", "activity_type"),
        Index("idx_activity_logs_document_type", "document_id", "activity_type"),
        Index("idx_activity_logs_workspace_type", "workspace_id", "activity_type"),
        Index("idx_activity_logs_user_created", "user_id", "created_at"),
    )


class ActivityFeed(Base):
    """Personalized activity feed for users."""
    
    __tablename__ = "activity_feeds"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    activity_id = Column(Integer, ForeignKey("activity_logs.id", ondelete="CASCADE"), nullable=False)
    
    # Feed configuration
    is_read = Column(Boolean, default=False, nullable=False)
    is_starred = Column(Boolean, default=False, nullable=False)
    is_hidden = Column(Boolean, default=False, nullable=False)
    
    # Relevance scoring
    relevance_score = Column(Integer, default=50, nullable=False)  # 0-100
    
    # Timestamps
    added_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    read_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User")
    activity = relationship("ActivityLog")
    
    # Indexes
    __table_args__ = (
        Index("idx_activity_feeds_user", "user_id"),
        Index("idx_activity_feeds_activity", "activity_id"),
        Index("idx_activity_feeds_read", "is_read"),
        Index("idx_activity_feeds_starred", "is_starred"),
        Index("idx_activity_feeds_hidden", "is_hidden"),
        Index("idx_activity_feeds_relevance", "relevance_score"),
        Index("idx_activity_feeds_added", "added_at"),
        # Composite indexes
        Index("idx_activity_feeds_user_read", "user_id", "is_read"),
        Index("idx_activity_feeds_user_added", "user_id", "added_at"),
    )


class NotificationPreference(Base):
    """User notification preferences for activity types."""
    
    __tablename__ = "notification_preferences"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    
    # Notification channels
    email_enabled = Column(Boolean, default=True, nullable=False)
    push_enabled = Column(Boolean, default=True, nullable=False)
    in_app_enabled = Column(Boolean, default=True, nullable=False)
    
    # Activity type preferences (JSON mapping)
    activity_preferences = Column(JSON, nullable=False, default=dict)
    
    # Frequency settings
    digest_frequency = Column(String(20), default="daily", nullable=False)  # immediate, hourly, daily, weekly
    quiet_hours_start = Column(String(5), nullable=True)  # HH:MM format
    quiet_hours_end = Column(String(5), nullable=True)    # HH:MM format
    
    # Workspace-specific preferences
    workspace_preferences = Column(JSON, nullable=False, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    user = relationship("User")
    
    # Indexes
    __table_args__ = (
        Index("idx_notification_preferences_user", "user_id"),
        Index("idx_notification_preferences_digest", "digest_frequency"),
    )


class ActivitySummary(Base):
    """Daily/weekly activity summaries for analytics."""
    
    __tablename__ = "activity_summaries"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Summary period
    summary_date = Column(DateTime(timezone=True), nullable=False)
    summary_type = Column(String(10), nullable=False)  # daily, weekly, monthly
    
    # Scope
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=True)
    workspace_id = Column(Integer, ForeignKey("workspaces.id", ondelete="CASCADE"), nullable=True)
    
    # Activity metrics
    total_activities = Column(Integer, default=0, nullable=False)
    document_activities = Column(Integer, default=0, nullable=False)
    comment_activities = Column(Integer, default=0, nullable=False)
    sharing_activities = Column(Integer, default=0, nullable=False)
    ai_activities = Column(Integer, default=0, nullable=False)
    
    # Engagement metrics
    active_users = Column(Integer, default=0, nullable=False)
    documents_created = Column(Integer, default=0, nullable=False)
    documents_shared = Column(Integer, default=0, nullable=False)
    comments_posted = Column(Integer, default=0, nullable=False)
    
    # Performance metrics
    avg_response_time = Column(Integer, nullable=True)  # milliseconds
    error_count = Column(Integer, default=0, nullable=False)
    
    # Additional metrics (JSON for flexibility)
    metrics_data = Column(JSON, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relationships
    user = relationship("User")
    workspace = relationship("Workspace")
    
    # Indexes
    __table_args__ = (
        Index("idx_activity_summaries_date", "summary_date"),
        Index("idx_activity_summaries_type", "summary_type"),
        Index("idx_activity_summaries_user", "user_id"),
        Index("idx_activity_summaries_workspace", "workspace_id"),
        # Composite indexes
        Index("idx_activity_summaries_user_date", "user_id", "summary_date"),
        Index("idx_activity_summaries_workspace_date", "workspace_id", "summary_date"),
    )


class SecurityEvent(Base):
    """Security-related events for audit and monitoring."""
    
    __tablename__ = "security_events"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Event classification
    event_type = Column(String(50), nullable=False)  # login_failed, suspicious_access, etc.
    severity = Column(String(20), nullable=False)    # low, medium, high, critical
    
    # Actor information
    user_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    ip_address = Column(String(45), nullable=False)
    user_agent = Column(Text, nullable=True)
    
    # Event details
    description = Column(Text, nullable=False)
    details = Column(JSON, nullable=True)
    
    # Response and resolution
    is_resolved = Column(Boolean, default=False, nullable=False)
    resolved_by_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    resolved_at = Column(DateTime(timezone=True), nullable=True)
    resolution_notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    resolved_by = relationship("User", foreign_keys=[resolved_by_id])
    
    # Indexes
    __table_args__ = (
        Index("idx_security_events_type", "event_type"),
        Index("idx_security_events_severity", "severity"),
        Index("idx_security_events_user", "user_id"),
        Index("idx_security_events_ip", "ip_address"),
        Index("idx_security_events_resolved", "is_resolved"),
        Index("idx_security_events_created", "created_at"),
    )
