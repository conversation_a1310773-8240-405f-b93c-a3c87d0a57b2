#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_standard_gamma_grad_ops.h>

namespace at {


// aten::_standard_gamma_grad(Tensor self, Tensor output) -> Tensor
inline at::Tensor _standard_gamma_grad(const at::Tensor & self, const at::Tensor & output) {
    return at::_ops::_standard_gamma_grad::call(self, output);
}

// aten::_standard_gamma_grad.out(Tensor self, Tensor output, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & _standard_gamma_grad_out(at::Tensor & out, const at::Tensor & self, const at::Tensor & output) {
    return at::_ops::_standard_gamma_grad_out::call(self, output, out);
}
// aten::_standard_gamma_grad.out(Tensor self, Tensor output, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & _standard_gamma_grad_outf(const at::Tensor & self, const at::Tensor & output, at::Tensor & out) {
    return at::_ops::_standard_gamma_grad_out::call(self, output, out);
}

}
