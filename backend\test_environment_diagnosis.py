#!/usr/bin/env python3
"""
Diagnose test environment issues and run basic validation.
"""

import sys
import os
import subprocess
import importlib.util
from pathlib import Path

def check_python_environment():
    """Check Python environment and basic imports."""
    print("🔍 Python Environment Diagnosis")
    print("=" * 50)
    
    print(f"Python Version: {sys.version}")
    print(f"Python Executable: {sys.executable}")
    print(f"Current Working Directory: {os.getcwd()}")
    print(f"Python Path: {sys.path[:3]}...")  # Show first 3 entries
    
    # Test basic imports
    try:
        import fastapi
        print(f"✅ FastAPI: {fastapi.__version__}")
    except ImportError as e:
        print(f"❌ FastAPI: {e}")
    
    try:
        import pydantic
        print(f"✅ Pydantic: {pydantic.__version__}")
    except ImportError as e:
        print(f"❌ Pydantic: {e}")
    
    try:
        import uvicorn
        print(f"✅ Uvicorn: {uvicorn.__version__}")
    except ImportError as e:
        print(f"❌ Uvicorn: {e}")
    
    try:
        import pytest
        print(f"✅ Pytest: {pytest.__version__}")
    except ImportError as e:
        print(f"❌ Pytest: {e}")
    
    print()

def check_app_import():
    """Check if the main app can be imported."""
    print("🔍 Application Import Test")
    print("=" * 50)
    
    try:
        # Add current directory to path
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))
        
        from app.main import app
        print("✅ Main app imported successfully")
        
        # Test app configuration
        print(f"✅ App title: {app.title}")
        print(f"✅ App version: {app.version}")
        
        return True
        
    except Exception as e:
        print(f"❌ App import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_simple_tests():
    """Run simple tests without pytest."""
    print("🔍 Simple Test Execution")
    print("=" * 50)
    
    try:
        # Test 1: Basic FastAPI functionality
        from fastapi.testclient import TestClient
        from app.main import app
        
        client = TestClient(app)
        
        # Test root endpoint
        response = client.get("/")
        print(f"✅ Root endpoint test: {response.status_code}")
        
        # Test health endpoint
        response = client.get("/health")
        print(f"✅ Health endpoint test: {response.status_code}")
        
        # Test API status endpoint
        response = client.get("/api/v1/status")
        print(f"✅ API status endpoint test: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ Simple tests failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_test_files():
    """Check available test files."""
    print("🔍 Test Files Analysis")
    print("=" * 50)
    
    test_dir = Path("tests")
    if test_dir.exists():
        test_files = list(test_dir.rglob("test_*.py"))
        print(f"✅ Found {len(test_files)} test files:")
        for test_file in test_files[:10]:  # Show first 10
            print(f"   - {test_file}")
        if len(test_files) > 10:
            print(f"   ... and {len(test_files) - 10} more")
    else:
        print("❌ Tests directory not found")
    
    print()

def run_linting_check():
    """Run basic linting check."""
    print("🔍 Code Quality Check")
    print("=" * 50)
    
    try:
        # Check if ruff is available
        result = subprocess.run(
            [sys.executable, "-m", "ruff", "--version"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print(f"✅ Ruff available: {result.stdout.strip()}")
            
            # Run basic ruff check
            result = subprocess.run(
                [sys.executable, "-m", "ruff", "check", "app/", "--select", "E,W,F"],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                print("✅ Basic linting passed")
            else:
                print(f"⚠️  Linting issues found: {result.stdout}")
        else:
            print("❌ Ruff not available")
    
    except Exception as e:
        print(f"❌ Linting check failed: {e}")

def main():
    """Main diagnosis function."""
    print("🚀 Backend Test Environment Diagnosis")
    print("=" * 60)
    print()
    
    # Run all checks
    check_python_environment()
    app_import_success = check_app_import()
    print()
    
    if app_import_success:
        run_simple_tests()
        print()
    
    check_test_files()
    run_linting_check()
    
    print()
    print("=" * 60)
    print("🎯 Diagnosis Complete")
    
    if app_import_success:
        print("✅ Core functionality is working")
        print("💡 Recommendation: Use Docker-based testing for comprehensive validation")
    else:
        print("❌ Core functionality has issues")
        print("💡 Recommendation: Fix import issues before proceeding")

if __name__ == "__main__":
    main()
