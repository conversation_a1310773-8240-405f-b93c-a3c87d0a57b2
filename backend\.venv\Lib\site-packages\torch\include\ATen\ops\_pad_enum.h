#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_pad_enum_ops.h>

namespace at {


// aten::_pad_enum(Tensor self, SymInt[] pad, int mode, float? value=None) -> Tensor
inline at::Tensor _pad_enum(const at::Tensor & self, at::IntArrayRef pad, int64_t mode, ::std::optional<double> value=::std::nullopt) {
    return at::_ops::_pad_enum::call(self, c10::fromIntArrayRefSlow(pad), mode, value);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, int64_t>>>
  at::Tensor _pad_enum(const at::Tensor & self, at::IntArrayRef pad, int64_t mode, ::std::optional<double> value=::std::nullopt) {
    return at::_ops::_pad_enum::call(self, c10::fromIntArrayRefSlow(pad), mode, value);
  }
}

// aten::_pad_enum(Tensor self, SymInt[] pad, int mode, float? value=None) -> Tensor
inline at::Tensor _pad_enum_symint(const at::Tensor & self, c10::SymIntArrayRef pad, int64_t mode, ::std::optional<double> value=::std::nullopt) {
    return at::_ops::_pad_enum::call(self, pad, mode, value);
}
namespace symint {
  template <typename T, typename = std::enable_if_t<std::is_same_v<T, c10::SymInt>>>
  at::Tensor _pad_enum(const at::Tensor & self, c10::SymIntArrayRef pad, int64_t mode, ::std::optional<double> value=::std::nullopt) {
    return at::_ops::_pad_enum::call(self, pad, mode, value);
  }
}

}
