#!/usr/bin/env python3
"""
Start the FastAPI server for development.
This script provides a reliable way to start the server without uvicorn CLI issues.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def main():
    """Start the FastAPI server."""
    try:
        # Import uvicorn and the app
        import uvicorn
        from app.main import app
        
        print("🚀 Starting Lonors AI Agent Platform Backend Server")
        print("📍 Server will be available at: http://localhost:3000")
        print("📖 API Documentation: http://localhost:3000/docs")
        print("🔧 Environment: Development")
        print("-" * 60)
        
        # Start the server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=3000,
            reload=True,
            log_level="info",
            access_log=True,
        )
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("🔧 Make sure all dependencies are installed:")
        print("   uv sync --dev")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Server Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
